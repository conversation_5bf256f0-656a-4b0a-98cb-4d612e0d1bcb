# Generated by Django 5.2.6 on 2025-09-05 23:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('home', 'المنزل'), ('work', 'العمل'), ('other', 'أخرى')], default='home', max_length=10, verbose_name='نوع العنوان')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.Char<PERSON>ield(max_length=50, verbose_name='الاسم الأخير')),
                ('company', models.CharField(blank=True, max_length=100, verbose_name='الشركة')),
                ('address_line_1', models.CharField(max_length=255, verbose_name='العنوان الأول')),
                ('address_line_2', models.CharField(blank=True, max_length=255, verbose_name='العنوان الثاني')),
                ('city', models.CharField(max_length=100, verbose_name='المدينة')),
                ('state', models.CharField(max_length=100, verbose_name='المنطقة/الولاية')),
                ('postal_code', models.CharField(max_length=20, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='السعودية', max_length=100, verbose_name='الدولة')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('is_default', models.BooleanField(default=False, verbose_name='العنوان الافتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'عنوان',
                'verbose_name_plural': 'العناوين',
                'ordering': ['-is_default', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='الصورة الشخصية')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='نبذة شخصية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'الملف الشخصي',
                'verbose_name_plural': 'الملفات الشخصية',
            },
        ),
    ]
