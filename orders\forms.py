from django import forms
from .models import Order


class OrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = [
            'payment_method', 'notes',
            # Billing address fields
            'billing_first_name', 'billing_last_name', 'billing_company',
            'billing_address_line_1', 'billing_address_line_2',
            'billing_city', 'billing_state', 'billing_postal_code',
            'billing_country', 'billing_phone', 'billing_email',
            # Shipping address fields
            'shipping_first_name', 'shipping_last_name', 'shipping_company',
            'shipping_address_line_1', 'shipping_address_line_2',
            'shipping_city', 'shipping_state', 'shipping_postal_code',
            'shipping_country', 'shipping_phone',
        ]
        
        labels = {
            'payment_method': 'طريقة الدفع',
            'notes': 'ملاحظات إضافية',
            # Billing labels
            'billing_first_name': 'الاسم الأول',
            'billing_last_name': 'الاسم الأخير',
            'billing_company': 'الشركة',
            'billing_address_line_1': 'العنوان الأول',
            'billing_address_line_2': 'العنوان الثاني',
            'billing_city': 'المدينة',
            'billing_state': 'المنطقة/الولاية',
            'billing_postal_code': 'الرمز البريدي',
            'billing_country': 'الدولة',
            'billing_phone': 'رقم الهاتف',
            'billing_email': 'البريد الإلكتروني',
            # Shipping labels
            'shipping_first_name': 'الاسم الأول',
            'shipping_last_name': 'الاسم الأخير',
            'shipping_company': 'الشركة',
            'shipping_address_line_1': 'العنوان الأول',
            'shipping_address_line_2': 'العنوان الثاني',
            'shipping_city': 'المدينة',
            'shipping_state': 'المنطقة/الولاية',
            'shipping_postal_code': 'الرمز البريدي',
            'shipping_country': 'الدولة',
            'shipping_phone': 'رقم الهاتف',
        }
        
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add CSS classes
        for field in self.fields:
            if field == 'notes':
                self.fields[field].widget.attrs.update({'class': 'form-control', 'placeholder': 'أي ملاحظات خاصة بالطلب...'})
            else:
                self.fields[field].widget.attrs.update({'class': 'form-control'})
        
        # Make required fields
        required_fields = [
            'billing_first_name', 'billing_last_name', 'billing_address_line_1',
            'billing_city', 'billing_state', 'billing_postal_code',
            'billing_phone', 'billing_email',
            'shipping_first_name', 'shipping_last_name', 'shipping_address_line_1',
            'shipping_city', 'shipping_state', 'shipping_postal_code',
            'shipping_phone',
        ]
        
        for field in required_fields:
            self.fields[field].required = True
