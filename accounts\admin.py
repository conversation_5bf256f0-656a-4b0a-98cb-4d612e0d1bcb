from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from .models import UserProfile, Address


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'الملف الشخصي'


class AddressInline(admin.TabularInline):
    model = Address
    extra = 0
    fields = ('type', 'first_name', 'last_name', 'city', 'phone', 'is_default')


class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline, AddressInline)


# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'phone', 'birth_date', 'avatar_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'user__email', 'phone')
    readonly_fields = ('created_at', 'updated_at')

    def avatar_preview(self, obj):
        if obj.avatar:
            return format_html('<img src="{}" width="50" height="50" />', obj.avatar.url)
        return "لا توجد صورة"
    avatar_preview.short_description = 'الصورة الشخصية'


@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    list_display = ('user', 'type', 'full_name', 'city', 'phone', 'is_default', 'created_at')
    list_filter = ('type', 'is_default', 'city', 'created_at')
    search_fields = ('user__username', 'first_name', 'last_name', 'city', 'phone')
    list_editable = ('is_default',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات المستخدم', {
            'fields': ('user', 'type', 'is_default')
        }),
        ('معلومات الاتصال', {
            'fields': ('first_name', 'last_name', 'company', 'phone')
        }),
        ('العنوان', {
            'fields': ('address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
