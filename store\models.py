from django.db import models
from django.urls import reverse
from django.contrib.auth.models import User
from PIL import Image


class Category(models.Model):
    name = models.CharField(max_length=200, verbose_name='اسم الفئة')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='الرابط')
    description = models.TextField(blank=True, verbose_name='الوصف')
    image = models.ImageField(upload_to='categories/', blank=True, null=True, verbose_name='الصورة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        ordering = ['name']
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('store:category_detail', args=[self.slug])


class Product(models.Model):
    AVAILABILITY_CHOICES = [
        ('in_stock', 'متوفر'),
        ('out_of_stock', 'غير متوفر'),
        ('limited', 'كمية محدودة'),
    ]

    name = models.CharField(max_length=200, verbose_name='اسم المنتج')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='الرابط')
    category = models.ForeignKey(Category, related_name='products', on_delete=models.CASCADE, verbose_name='الفئة')
    description = models.TextField(verbose_name='الوصف')
    short_description = models.CharField(max_length=500, blank=True, verbose_name='وصف مختصر')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر')
    discount_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name='سعر الخصم')
    stock = models.PositiveIntegerField(default=0, verbose_name='الكمية المتوفرة')
    availability = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='in_stock', verbose_name='حالة التوفر')
    weight = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, verbose_name='الوزن (كجم)')
    dimensions = models.CharField(max_length=100, blank=True, verbose_name='الأبعاد')
    sku = models.CharField(max_length=50, unique=True, verbose_name='رمز المنتج')
    is_featured = models.BooleanField(default=False, verbose_name='منتج مميز')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('store:product_detail', args=[self.slug])

    @property
    def get_price(self):
        """Return the discount price if available, otherwise return the regular price"""
        if self.discount_price:
            return self.discount_price
        return self.price

    @property
    def get_discount_percentage(self):
        """Calculate discount percentage"""
        if self.discount_price and self.price > self.discount_price:
            return int(((self.price - self.discount_price) / self.price) * 100)
        return 0

    @property
    def is_in_stock(self):
        """Check if product is in stock"""
        return self.stock > 0 and self.availability == 'in_stock'


class ProductImage(models.Model):
    product = models.ForeignKey(Product, related_name='images', on_delete=models.CASCADE, verbose_name='المنتج')
    image = models.ImageField(upload_to='products/', verbose_name='الصورة')
    alt_text = models.CharField(max_length=200, blank=True, verbose_name='النص البديل')
    is_main = models.BooleanField(default=False, verbose_name='الصورة الرئيسية')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        ordering = ['-is_main', 'created_at']
        verbose_name = 'صورة المنتج'
        verbose_name_plural = 'صور المنتجات'

    def __str__(self):
        return f'{self.product.name} - صورة'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # Resize image if it's too large
        if self.image:
            img = Image.open(self.image.path)
            if img.height > 800 or img.width > 800:
                output_size = (800, 800)
                img.thumbnail(output_size)
                img.save(self.image.path)


class ProductReview(models.Model):
    RATING_CHOICES = [
        (1, '1 نجمة'),
        (2, '2 نجمة'),
        (3, '3 نجوم'),
        (4, '4 نجوم'),
        (5, '5 نجوم'),
    ]

    product = models.ForeignKey(Product, related_name='reviews', on_delete=models.CASCADE, verbose_name='المنتج')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    rating = models.IntegerField(choices=RATING_CHOICES, verbose_name='التقييم')
    comment = models.TextField(verbose_name='التعليق')
    is_approved = models.BooleanField(default=False, verbose_name='معتمد')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        ordering = ['-created_at']
        unique_together = ('product', 'user')
        verbose_name = 'تقييم المنتج'
        verbose_name_plural = 'تقييمات المنتجات'

    def __str__(self):
        return f'{self.product.name} - {self.rating} نجوم'


class Wishlist(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='المنتج')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')

    class Meta:
        unique_together = ('user', 'product')
        verbose_name = 'قائمة الأمنيات'
        verbose_name_plural = 'قوائم الأمنيات'

    def __str__(self):
        return f'{self.user.username} - {self.product.name}'
