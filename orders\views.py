from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from decimal import Decimal
from cart.cart import Cart
from accounts.models import Address
from .models import Order, OrderItem, OrderStatusHistory
from .forms import OrderForm


@login_required
def checkout(request):
    """Checkout process"""
    cart = Cart(request)
    
    if len(cart) == 0:
        messages.error(request, 'سلة التسوق فارغة')
        return redirect('cart:cart_detail')
    
    # Get user's addresses
    addresses = Address.objects.filter(user=request.user)
    default_address = addresses.filter(is_default=True).first()
    
    if request.method == 'POST':
        form = OrderForm(request.POST)
        if form.is_valid():
            # Create order
            order = form.save(commit=False)
            order.user = request.user
            
            # Calculate totals
            subtotal = cart.get_total_price()
            shipping_cost = Decimal('25.00')  # Fixed shipping cost
            tax_rate = Decimal('0.15')  # 15% VAT
            tax_amount = subtotal * tax_rate
            total = subtotal + shipping_cost + tax_amount
            
            order.subtotal = subtotal
            order.shipping_cost = shipping_cost
            order.tax_amount = tax_amount
            order.total = total
            
            order.save()
            
            # Create order items
            for item in cart:
                OrderItem.objects.create(
                    order=order,
                    product=item['product'],
                    quantity=item['quantity'],
                    price=item['price']
                )
                
                # Update product stock
                product = item['product']
                product.stock -= item['quantity']
                product.save()
            
            # Create initial status history
            OrderStatusHistory.objects.create(
                order=order,
                status='pending',
                notes='تم إنشاء الطلب'
            )
            
            # Clear cart
            cart.clear()
            
            messages.success(request, f'تم إنشاء طلبك بنجاح! رقم الطلب: {order.order_number}')
            return redirect('orders:order_detail', order_id=order.id)
    else:
        form = OrderForm()
        
        # Pre-populate form with default address if available
        if default_address:
            form.fields['billing_first_name'].initial = default_address.first_name
            form.fields['billing_last_name'].initial = default_address.last_name
            form.fields['billing_company'].initial = default_address.company
            form.fields['billing_address_line_1'].initial = default_address.address_line_1
            form.fields['billing_address_line_2'].initial = default_address.address_line_2
            form.fields['billing_city'].initial = default_address.city
            form.fields['billing_state'].initial = default_address.state
            form.fields['billing_postal_code'].initial = default_address.postal_code
            form.fields['billing_country'].initial = default_address.country
            form.fields['billing_phone'].initial = default_address.phone
            form.fields['billing_email'].initial = request.user.email
            
            # Copy billing to shipping
            form.fields['shipping_first_name'].initial = default_address.first_name
            form.fields['shipping_last_name'].initial = default_address.last_name
            form.fields['shipping_company'].initial = default_address.company
            form.fields['shipping_address_line_1'].initial = default_address.address_line_1
            form.fields['shipping_address_line_2'].initial = default_address.address_line_2
            form.fields['shipping_city'].initial = default_address.city
            form.fields['shipping_state'].initial = default_address.state
            form.fields['shipping_postal_code'].initial = default_address.postal_code
            form.fields['shipping_country'].initial = default_address.country
            form.fields['shipping_phone'].initial = default_address.phone
    
    # Calculate totals for display
    subtotal = cart.get_total_price()
    shipping_cost = Decimal('25.00')
    tax_rate = Decimal('0.15')
    tax_amount = subtotal * tax_rate
    total = subtotal + shipping_cost + tax_amount
    
    context = {
        'form': form,
        'cart': cart,
        'addresses': addresses,
        'subtotal': subtotal,
        'shipping_cost': shipping_cost,
        'tax_amount': tax_amount,
        'total': total,
    }
    return render(request, 'orders/checkout.html', context)


@login_required
def order_list(request):
    """User's order history"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')
    
    # Pagination
    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'orders/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """Order detail view"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    order_items = order.items.all()
    status_history = order.status_history.all()
    
    context = {
        'order': order,
        'order_items': order_items,
        'status_history': status_history,
    }
    return render(request, 'orders/order_detail.html', context)


@login_required
def cancel_order(request, order_id):
    """Cancel order (only if pending or confirmed)"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    
    if order.status in ['pending', 'confirmed']:
        if request.method == 'POST':
            # Update order status
            order.status = 'cancelled'
            order.save()
            
            # Restore product stock
            for item in order.items.all():
                product = item.product
                product.stock += item.quantity
                product.save()
            
            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                status='cancelled',
                notes='تم إلغاء الطلب بواسطة العميل'
            )
            
            messages.success(request, 'تم إلغاء الطلب بنجاح')
            return redirect('orders:order_detail', order_id=order.id)
        
        return render(request, 'orders/cancel_order.html', {'order': order})
    else:
        messages.error(request, 'لا يمكن إلغاء هذا الطلب')
        return redirect('orders:order_detail', order_id=order.id)
