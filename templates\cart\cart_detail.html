{% extends 'base.html' %}
{% load static %}
{% load math_filters %}

{% block title %}سلة التسوق - متجر ثماري{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="bi bi-cart3"></i> سلة التسوق
                {% if cart|length > 0 %}
                    <span class="badge bg-primary">{{ cart|length }}</span>
                {% endif %}
            </h2>
        </div>
    </div>
    
    {% if cart|length > 0 %}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table cart-table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in cart %}
                                <tr data-product-id="{{ item.product.id }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if item.product.images.first %}
                                                <img src="{{ item.product.images.first.image.url }}" 
                                                     alt="{{ item.product.name }}" 
                                                     class="me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                            {% else %}
                                                <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 80px; height: 80px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-1">
                                                    <a href="{% url 'store:product_detail' item.product.slug %}" 
                                                       class="text-decoration-none">
                                                        {{ item.product.name }}
                                                    </a>
                                                </h6>
                                                <small class="text-muted">{{ item.product.category.name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ item.price }} ر.س</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <button class="btn btn-outline-secondary btn-sm me-2" 
                                                    onclick="updateQuantity({{ item.product.id }}, {{ item.quantity|add:'-1' }})">
                                                <i class="bi bi-dash"></i>
                                            </button>
                                            <input type="number" class="form-control quantity-input" 
                                                   value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}"
                                                   onchange="updateQuantity({{ item.product.id }}, this.value)">
                                            <button class="btn btn-outline-secondary btn-sm ms-2" 
                                                    onclick="updateQuantity({{ item.product.id }}, {{ item.quantity|add:'1' }})">
                                                <i class="bi bi-plus"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted d-block mt-1">متوفر: {{ item.product.stock }}</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold item-total">{{ item.total_price }} ر.س</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="removeFromCart({{ item.product.id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-3">
                        <a href="{% url 'store:product_list' %}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> متابعة التسوق
                        </a>
                        <button class="btn btn-outline-danger" onclick="clearCart()">
                            <i class="bi bi-trash"></i> إفراغ السلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span class="fw-bold" id="cart-subtotal">{{ subtotal }} ر.س</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span>{{ shipping }} ر.س</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة (15%):</span>
                        <span id="tax-amount">{{ tax_amount|floatformat:2 }} ر.س</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <span class="fw-bold">المجموع الكلي:</span>
                        <span class="fw-bold text-primary" id="cart-total">
                            {{ total|floatformat:2 }} ر.س
                        </span>
                    </div>
                    
                    {% if user.is_authenticated %}
                        <div class="d-grid">
                            <a href="{% url 'orders:checkout' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-credit-card"></i> إتمام الشراء
                            </a>
                        </div>
                    {% else %}
                        <div class="d-grid gap-2">
                            <a href="{% url 'accounts:login' %}?next={% url 'orders:checkout' %}" 
                               class="btn btn-primary">
                                تسجيل الدخول للشراء
                            </a>
                            <a href="{% url 'accounts:register' %}" class="btn btn-outline-primary">
                                إنشاء حساب جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Recommended Products -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">منتجات مقترحة</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">سيتم عرض منتجات مقترحة هنا</p>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- Empty Cart -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-cart-x text-muted" style="font-size: 5rem;"></i>
                <h3 class="mt-3">سلة التسوق فارغة</h3>
                <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
                <a href="{% url 'store:product_list' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-shop"></i> ابدأ التسوق
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateQuantity(productId, quantity) {
    if (quantity < 1) {
        removeFromCart(productId);
        return;
    }
    
    const formData = new FormData();
    formData.append('quantity', quantity);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch(`/cart/update/${productId}/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update item total
            const row = document.querySelector(`tr[data-product-id="${productId}"]`);
            row.querySelector('.item-total').textContent = data.item_total + ' ر.س';
            
            // Update cart totals
            updateCartTotals(data.cart_total);
            
            // Update cart count in navbar
            document.getElementById('cart-count').textContent = data.cart_count;
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الكمية');
    });
}

function removeFromCart(productId) {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch(`/cart/remove/${productId}/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove row
            const row = document.querySelector(`tr[data-product-id="${productId}"]`);
            row.remove();
            
            // Update cart totals
            updateCartTotals(data.cart_total);
            
            // Update cart count in navbar
            document.getElementById('cart-count').textContent = data.cart_count;
            
            // Reload page if cart is empty
            if (data.cart_count == 0) {
                location.reload();
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حذف المنتج');
    });
}

function clearCart() {
    if (!confirm('هل أنت متأكد من إفراغ السلة؟')) {
        return;
    }
    
    fetch('/cart/clear/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إفراغ السلة');
    });
}

function updateCartTotals(subtotal) {
    const subtotalFloat = parseFloat(subtotal);
    const shipping = 25.00;
    const tax = subtotalFloat * 0.15;
    const total = subtotalFloat + shipping + tax;
    
    document.getElementById('cart-subtotal').textContent = subtotalFloat.toFixed(2) + ' ر.س';
    document.getElementById('tax-amount').textContent = tax.toFixed(2) + ' ر.س';
    document.getElementById('cart-total').textContent = total.toFixed(2) + ' ر.س';
}
</script>
{% endblock %}
