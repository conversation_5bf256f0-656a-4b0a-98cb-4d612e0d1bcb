from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from store.models import Product
from .cart import Cart


def cart_detail(request):
    """Display cart contents"""
    cart = Cart(request)

    # Calculate totals
    cart_items = []
    for item in cart:
        cart_items.append(item)

    # Calculate totals for template
    subtotal = cart.get_total_price()
    shipping = 25.00
    tax_rate = 0.15
    tax_amount = subtotal * tax_rate
    total = subtotal + shipping + tax_amount

    context = {
        'cart': cart,
        'cart_items': cart_items,
        'subtotal': subtotal,
        'shipping': shipping,
        'tax_amount': tax_amount,
        'total': total,
    }
    return render(request, 'cart/cart_detail.html', context)


@require_POST
def cart_add(request, product_id):
    """Add product to cart"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id, is_active=True)
    quantity = int(request.POST.get('quantity', 1))
    
    # Check if product is in stock
    if not product.is_in_stock:
        messages.error(request, 'هذا المنتج غير متوفر حالياً')
        return redirect('store:product_detail', slug=product.slug)
    
    # Check if requested quantity is available
    if quantity > product.stock:
        messages.error(request, f'الكمية المطلوبة غير متوفرة. المتوفر: {product.stock}')
        return redirect('store:product_detail', slug=product.slug)
    
    cart.add(product=product, quantity=quantity)
    messages.success(request, f'تم إضافة {product.name} إلى سلة التسوق')
    
    # Return JSON response for AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': f'تم إضافة {product.name} إلى سلة التسوق',
            'cart_count': len(cart),
            'cart_total': str(cart.get_total_price())
        })
    
    return redirect('cart:cart_detail')


@require_POST
def cart_remove(request, product_id):
    """Remove product from cart"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id)
    cart.remove(product)
    messages.success(request, f'تم حذف {product.name} من سلة التسوق')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': f'تم حذف {product.name} من سلة التسوق',
            'cart_count': len(cart),
            'cart_total': str(cart.get_total_price())
        })
    
    return redirect('cart:cart_detail')


@require_POST
def cart_update(request, product_id):
    """Update product quantity in cart"""
    cart = Cart(request)
    product = get_object_or_404(Product, id=product_id)
    quantity = int(request.POST.get('quantity', 1))
    
    if quantity > 0:
        # Check if requested quantity is available
        if quantity > product.stock:
            messages.error(request, f'الكمية المطلوبة غير متوفرة. المتوفر: {product.stock}')
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': f'الكمية المطلوبة غير متوفرة. المتوفر: {product.stock}'
                })
            return redirect('cart:cart_detail')
        
        cart.update_quantity(product, quantity)
        messages.success(request, 'تم تحديث الكمية بنجاح')
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # Calculate item total
            item_total = product.get_price * quantity
            return JsonResponse({
                'success': True,
                'message': 'تم تحديث الكمية بنجاح',
                'item_total': str(item_total),
                'cart_count': len(cart),
                'cart_total': str(cart.get_total_price())
            })
    else:
        cart.remove(product)
        messages.success(request, f'تم حذف {product.name} من سلة التسوق')
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'تم حذف {product.name} من سلة التسوق',
                'cart_count': len(cart),
                'cart_total': str(cart.get_total_price())
            })
    
    return redirect('cart:cart_detail')


def cart_clear(request):
    """Clear all items from cart"""
    cart = Cart(request)
    cart.clear()
    messages.success(request, 'تم إفراغ سلة التسوق')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': 'تم إفراغ سلة التسوق',
            'cart_count': 0,
            'cart_total': '0.00'
        })
    
    return redirect('cart:cart_detail')


def cart_count(request):
    """Get cart item count (for AJAX)"""
    cart = Cart(request)
    return JsonResponse({
        'cart_count': len(cart),
        'cart_total': str(cart.get_total_price())
    })
