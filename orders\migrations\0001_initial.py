# Generated by Django 5.2.6 on 2025-09-05 23:37

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('store', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('confirmed', 'مؤكد'), ('processing', 'قيد المعالجة'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي'), ('refunded', 'مسترد')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('payment_status', models.CharField(choices=[('pending', 'في الانتظار'), ('paid', 'مدفوع'), ('failed', 'فشل'), ('refunded', 'مسترد')], default='pending', max_length=20, verbose_name='حالة الدفع')),
                ('payment_method', models.CharField(choices=[('cash_on_delivery', 'الدفع عند الاستلام'), ('bank_transfer', 'تحويل بنكي'), ('credit_card', 'بطاقة ائتمان'), ('mada', 'مدى')], default='cash_on_delivery', max_length=20, verbose_name='طريقة الدفع')),
                ('billing_first_name', models.CharField(max_length=50, verbose_name='الاسم الأول للفاتورة')),
                ('billing_last_name', models.CharField(max_length=50, verbose_name='الاسم الأخير للفاتورة')),
                ('billing_company', models.CharField(blank=True, max_length=100, verbose_name='الشركة')),
                ('billing_address_line_1', models.CharField(max_length=255, verbose_name='عنوان الفاتورة الأول')),
                ('billing_address_line_2', models.CharField(blank=True, max_length=255, verbose_name='عنوان الفاتورة الثاني')),
                ('billing_city', models.CharField(max_length=100, verbose_name='مدينة الفاتورة')),
                ('billing_state', models.CharField(max_length=100, verbose_name='منطقة الفاتورة')),
                ('billing_postal_code', models.CharField(max_length=20, verbose_name='الرمز البريدي للفاتورة')),
                ('billing_country', models.CharField(default='السعودية', max_length=100, verbose_name='دولة الفاتورة')),
                ('billing_phone', models.CharField(max_length=20, verbose_name='هاتف الفاتورة')),
                ('billing_email', models.EmailField(max_length=254, verbose_name='بريد الفاتورة')),
                ('shipping_first_name', models.CharField(max_length=50, verbose_name='الاسم الأول للشحن')),
                ('shipping_last_name', models.CharField(max_length=50, verbose_name='الاسم الأخير للشحن')),
                ('shipping_company', models.CharField(blank=True, max_length=100, verbose_name='شركة الشحن')),
                ('shipping_address_line_1', models.CharField(max_length=255, verbose_name='عنوان الشحن الأول')),
                ('shipping_address_line_2', models.CharField(blank=True, max_length=255, verbose_name='عنوان الشحن الثاني')),
                ('shipping_city', models.CharField(max_length=100, verbose_name='مدينة الشحن')),
                ('shipping_state', models.CharField(max_length=100, verbose_name='منطقة الشحن')),
                ('shipping_postal_code', models.CharField(max_length=20, verbose_name='الرمز البريدي للشحن')),
                ('shipping_country', models.CharField(default='السعودية', max_length=100, verbose_name='دولة الشحن')),
                ('shipping_phone', models.CharField(max_length=20, verbose_name='هاتف الشحن')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة الشحن')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الضريبة')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخصم')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الكلي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('tracking_number', models.CharField(blank=True, max_length=100, verbose_name='رقم التتبع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('shipped_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر الطلب',
                'verbose_name_plural': 'عناصر الطلبات',
            },
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('confirmed', 'مؤكد'), ('processing', 'قيد المعالجة'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي'), ('refunded', 'مسترد')], max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'تاريخ حالة الطلب',
                'verbose_name_plural': 'تاريخ حالات الطلبات',
                'ordering': ['-created_at'],
            },
        ),
    ]
