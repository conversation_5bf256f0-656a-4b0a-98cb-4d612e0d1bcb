from django.db import models
from django.contrib.auth.models import User
from store.models import Product
import uuid


class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('confirmed', 'مؤكد'),
        ('processing', 'قيد المعالجة'),
        ('shipped', 'تم الشحن'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
        ('refunded', 'مسترد'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('paid', 'مدفوع'),
        ('failed', 'فشل'),
        ('refunded', 'مسترد'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('cash_on_delivery', 'الدفع عند الاستلام'),
        ('bank_transfer', 'تحويل بنكي'),
        ('credit_card', 'بطاقة ائتمان'),
        ('mada', 'مدى'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, related_name='orders', on_delete=models.CASCADE, verbose_name='المستخدم')
    order_number = models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='حالة الطلب')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending', verbose_name='حالة الدفع')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash_on_delivery', verbose_name='طريقة الدفع')
    
    # Billing Address
    billing_first_name = models.CharField(max_length=50, verbose_name='الاسم الأول للفاتورة')
    billing_last_name = models.CharField(max_length=50, verbose_name='الاسم الأخير للفاتورة')
    billing_company = models.CharField(max_length=100, blank=True, verbose_name='الشركة')
    billing_address_line_1 = models.CharField(max_length=255, verbose_name='عنوان الفاتورة الأول')
    billing_address_line_2 = models.CharField(max_length=255, blank=True, verbose_name='عنوان الفاتورة الثاني')
    billing_city = models.CharField(max_length=100, verbose_name='مدينة الفاتورة')
    billing_state = models.CharField(max_length=100, verbose_name='منطقة الفاتورة')
    billing_postal_code = models.CharField(max_length=20, verbose_name='الرمز البريدي للفاتورة')
    billing_country = models.CharField(max_length=100, default='السعودية', verbose_name='دولة الفاتورة')
    billing_phone = models.CharField(max_length=20, verbose_name='هاتف الفاتورة')
    billing_email = models.EmailField(verbose_name='بريد الفاتورة')
    
    # Shipping Address
    shipping_first_name = models.CharField(max_length=50, verbose_name='الاسم الأول للشحن')
    shipping_last_name = models.CharField(max_length=50, verbose_name='الاسم الأخير للشحن')
    shipping_company = models.CharField(max_length=100, blank=True, verbose_name='شركة الشحن')
    shipping_address_line_1 = models.CharField(max_length=255, verbose_name='عنوان الشحن الأول')
    shipping_address_line_2 = models.CharField(max_length=255, blank=True, verbose_name='عنوان الشحن الثاني')
    shipping_city = models.CharField(max_length=100, verbose_name='مدينة الشحن')
    shipping_state = models.CharField(max_length=100, verbose_name='منطقة الشحن')
    shipping_postal_code = models.CharField(max_length=20, verbose_name='الرمز البريدي للشحن')
    shipping_country = models.CharField(max_length=100, default='السعودية', verbose_name='دولة الشحن')
    shipping_phone = models.CharField(max_length=20, verbose_name='هاتف الشحن')
    
    # Order totals
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المجموع الفرعي')
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='تكلفة الشحن')
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الضريبة')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخصم')
    total = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المجموع الكلي')
    
    # Additional fields
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    tracking_number = models.CharField(max_length=100, blank=True, verbose_name='رقم التتبع')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    shipped_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')
    delivered_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التسليم')

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبات'

    def __str__(self):
        return f'طلب #{self.order_number}'

    def save(self, *args, **kwargs):
        if not self.order_number:
            # Generate order number
            import random
            import string
            self.order_number = ''.join(random.choices(string.digits, k=8))
        super().save(*args, **kwargs)

    @property
    def billing_full_name(self):
        return f'{self.billing_first_name} {self.billing_last_name}'

    @property
    def shipping_full_name(self):
        return f'{self.shipping_first_name} {self.shipping_last_name}'

    @property
    def billing_full_address(self):
        address_parts = [self.billing_address_line_1]
        if self.billing_address_line_2:
            address_parts.append(self.billing_address_line_2)
        address_parts.extend([self.billing_city, self.billing_state, self.billing_postal_code, self.billing_country])
        return ', '.join(address_parts)

    @property
    def shipping_full_address(self):
        address_parts = [self.shipping_address_line_1]
        if self.shipping_address_line_2:
            address_parts.append(self.shipping_address_line_2)
        address_parts.extend([self.shipping_city, self.shipping_state, self.shipping_postal_code, self.shipping_country])
        return ', '.join(address_parts)


class OrderItem(models.Model):
    order = models.ForeignKey(Order, related_name='items', on_delete=models.CASCADE, verbose_name='الطلب')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='المنتج')
    quantity = models.PositiveIntegerField(verbose_name='الكمية')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر')
    total = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المجموع')

    class Meta:
        verbose_name = 'عنصر الطلب'
        verbose_name_plural = 'عناصر الطلبات'

    def __str__(self):
        return f'{self.product.name} x {self.quantity}'

    def save(self, *args, **kwargs):
        self.total = self.price * self.quantity
        super().save(*args, **kwargs)


class OrderStatusHistory(models.Model):
    order = models.ForeignKey(Order, related_name='status_history', on_delete=models.CASCADE, verbose_name='الطلب')
    status = models.CharField(max_length=20, choices=Order.STATUS_CHOICES, verbose_name='الحالة')
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم بواسطة')

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'تاريخ حالة الطلب'
        verbose_name_plural = 'تاريخ حالات الطلبات'

    def __str__(self):
        return f'{self.order.order_number} - {self.get_status_display()}'
