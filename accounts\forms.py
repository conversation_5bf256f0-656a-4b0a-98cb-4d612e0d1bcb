from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import UserProfile, Address


class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(required=True, label='البريد الإلكتروني')
    first_name = forms.CharField(max_length=30, required=True, label='الاسم الأول')
    last_name = forms.CharField(max_length=30, required=True, label='الاسم الأخير')

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
        labels = {
            'username': 'اسم المستخدم',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].help_text = None
        self.fields['password1'].help_text = None
        self.fields['password2'].help_text = None
        
        # Add CSS classes
        for field in self.fields:
            self.fields[field].widget.attrs.update({'class': 'form-control'})

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()
        return user


class UserProfileForm(forms.ModelForm):
    first_name = forms.CharField(max_length=30, required=False, label='الاسم الأول')
    last_name = forms.CharField(max_length=30, required=False, label='الاسم الأخير')
    email = forms.EmailField(required=False, label='البريد الإلكتروني')

    class Meta:
        model = UserProfile
        fields = ['phone', 'birth_date', 'avatar', 'bio']
        labels = {
            'phone': 'رقم الهاتف',
            'birth_date': 'تاريخ الميلاد',
            'avatar': 'الصورة الشخصية',
            'bio': 'نبذة شخصية',
        }
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes
        for field in self.fields:
            if field == 'avatar':
                self.fields[field].widget.attrs.update({'class': 'form-control-file'})
            else:
                self.fields[field].widget.attrs.update({'class': 'form-control'})


class AddressForm(forms.ModelForm):
    class Meta:
        model = Address
        fields = [
            'type', 'first_name', 'last_name', 'company',
            'address_line_1', 'address_line_2', 'city', 'state',
            'postal_code', 'country', 'phone', 'is_default'
        ]
        labels = {
            'type': 'نوع العنوان',
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'company': 'الشركة',
            'address_line_1': 'العنوان الأول',
            'address_line_2': 'العنوان الثاني',
            'city': 'المدينة',
            'state': 'المنطقة/الولاية',
            'postal_code': 'الرمز البريدي',
            'country': 'الدولة',
            'phone': 'رقم الهاتف',
            'is_default': 'العنوان الافتراضي',
        }
        widgets = {
            'type': forms.Select(attrs={'class': 'form-control'}),
            'is_default': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes
        for field in self.fields:
            if field == 'is_default':
                continue
            self.fields[field].widget.attrs.update({'class': 'form-control'})
        
        # Make some fields required
        self.fields['first_name'].required = True
        self.fields['last_name'].required = True
        self.fields['address_line_1'].required = True
        self.fields['city'].required = True
        self.fields['state'].required = True
        self.fields['postal_code'].required = True
        self.fields['phone'].required = True
