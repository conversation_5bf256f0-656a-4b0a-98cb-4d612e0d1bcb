# Generated by Django 5.2.6 on 2025-09-05 23:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الفئة')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, null=True, upload_to='categories/', verbose_name='الصورة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('short_description', models.CharField(blank=True, max_length=500, verbose_name='وصف مختصر')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('discount_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الخصم')),
                ('stock', models.PositiveIntegerField(default=0, verbose_name='الكمية المتوفرة')),
                ('availability', models.CharField(choices=[('in_stock', 'متوفر'), ('out_of_stock', 'غير متوفر'), ('limited', 'كمية محدودة')], default='in_stock', max_length=20, verbose_name='حالة التوفر')),
                ('weight', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='الوزن (كجم)')),
                ('dimensions', models.CharField(blank=True, max_length=100, verbose_name='الأبعاد')),
                ('sku', models.CharField(max_length=50, unique=True, verbose_name='رمز المنتج')),
                ('is_featured', models.BooleanField(default=False, verbose_name='منتج مميز')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='store.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/', verbose_name='الصورة')),
                ('alt_text', models.CharField(blank=True, max_length=200, verbose_name='النص البديل')),
                ('is_main', models.BooleanField(default=False, verbose_name='الصورة الرئيسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='store.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة المنتج',
                'verbose_name_plural': 'صور المنتجات',
                'ordering': ['-is_main', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, '1 نجمة'), (2, '2 نجمة'), (3, '3 نجوم'), (4, '4 نجوم'), (5, '5 نجوم')], verbose_name='التقييم')),
                ('comment', models.TextField(verbose_name='التعليق')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='store.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تقييم المنتج',
                'verbose_name_plural': 'تقييمات المنتجات',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'user')},
            },
        ),
        migrations.CreateModel(
            name='Wishlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='store.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'قائمة الأمنيات',
                'verbose_name_plural': 'قوائم الأمنيات',
                'unique_together': {('user', 'product')},
            },
        ),
    ]
