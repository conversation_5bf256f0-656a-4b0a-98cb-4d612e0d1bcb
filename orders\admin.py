from django.contrib import admin
from django.utils.html import format_html
from .models import Order, OrderItem, OrderStatusHistory


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ('total',)


class OrderStatusHistoryInline(admin.TabularInline):
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ('created_at', 'created_by')


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'user', 'status', 'payment_status', 'payment_method', 'total', 'created_at')
    list_filter = ('status', 'payment_status', 'payment_method', 'created_at', 'shipped_at', 'delivered_at')
    search_fields = ('order_number', 'user__username', 'user__email', 'billing_email', 'tracking_number')
    readonly_fields = ('id', 'order_number', 'created_at', 'updated_at')
    list_editable = ('status', 'payment_status')
    inlines = [OrderItemInline, OrderStatusHistoryInline]
    
    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('id', 'order_number', 'user', 'status', 'payment_status', 'payment_method', 'notes')
        }),
        ('عنوان الفاتورة', {
            'fields': (
                ('billing_first_name', 'billing_last_name'),
                'billing_company',
                'billing_address_line_1',
                'billing_address_line_2',
                ('billing_city', 'billing_state', 'billing_postal_code'),
                'billing_country',
                ('billing_phone', 'billing_email')
            )
        }),
        ('عنوان الشحن', {
            'fields': (
                ('shipping_first_name', 'shipping_last_name'),
                'shipping_company',
                'shipping_address_line_1',
                'shipping_address_line_2',
                ('shipping_city', 'shipping_state', 'shipping_postal_code'),
                'shipping_country',
                'shipping_phone'
            )
        }),
        ('المبالغ', {
            'fields': (
                ('subtotal', 'shipping_cost'),
                ('tax_amount', 'discount_amount'),
                'total'
            )
        }),
        ('الشحن والتتبع', {
            'fields': ('tracking_number', 'shipped_at', 'delivered_at')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # Save the order first
        super().save_model(request, obj, form, change)
        
        # If status changed, create a status history entry
        if change and 'status' in form.changed_data:
            OrderStatusHistory.objects.create(
                order=obj,
                status=obj.status,
                created_by=request.user,
                notes=f'تم تغيير الحالة إلى {obj.get_status_display()}'
            )


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ('order', 'product', 'quantity', 'price', 'total')
    list_filter = ('order__created_at',)
    search_fields = ('order__order_number', 'product__name')
    readonly_fields = ('total',)


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ('order', 'status', 'created_at', 'created_by')
    list_filter = ('status', 'created_at')
    search_fields = ('order__order_number', 'notes')
    readonly_fields = ('created_at',)
