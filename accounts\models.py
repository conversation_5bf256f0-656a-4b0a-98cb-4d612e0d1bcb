from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from PIL import Image


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    phone = models.CharField(max_length=20, blank=True, verbose_name='رقم الهاتف')
    birth_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name='الصورة الشخصية')
    bio = models.TextField(max_length=500, blank=True, verbose_name='نبذة شخصية')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'الملف الشخصي'
        verbose_name_plural = 'الملفات الشخصية'

    def __str__(self):
        return f'{self.user.username} - الملف الشخصي'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # Resize avatar if it's too large
        if self.avatar:
            img = Image.open(self.avatar.path)
            if img.height > 300 or img.width > 300:
                output_size = (300, 300)
                img.thumbnail(output_size)
                img.save(self.avatar.path)


class Address(models.Model):
    ADDRESS_TYPES = [
        ('home', 'المنزل'),
        ('work', 'العمل'),
        ('other', 'أخرى'),
    ]

    user = models.ForeignKey(User, related_name='addresses', on_delete=models.CASCADE, verbose_name='المستخدم')
    type = models.CharField(max_length=10, choices=ADDRESS_TYPES, default='home', verbose_name='نوع العنوان')
    first_name = models.CharField(max_length=50, verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=50, verbose_name='الاسم الأخير')
    company = models.CharField(max_length=100, blank=True, verbose_name='الشركة')
    address_line_1 = models.CharField(max_length=255, verbose_name='العنوان الأول')
    address_line_2 = models.CharField(max_length=255, blank=True, verbose_name='العنوان الثاني')
    city = models.CharField(max_length=100, verbose_name='المدينة')
    state = models.CharField(max_length=100, verbose_name='المنطقة/الولاية')
    postal_code = models.CharField(max_length=20, verbose_name='الرمز البريدي')
    country = models.CharField(max_length=100, default='السعودية', verbose_name='الدولة')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    is_default = models.BooleanField(default=False, verbose_name='العنوان الافتراضي')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        ordering = ['-is_default', '-created_at']
        verbose_name = 'عنوان'
        verbose_name_plural = 'العناوين'

    def __str__(self):
        return f'{self.first_name} {self.last_name} - {self.city}'

    def save(self, *args, **kwargs):
        # If this address is set as default, remove default from other addresses
        if self.is_default:
            Address.objects.filter(user=self.user, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'

    @property
    def full_address(self):
        address_parts = [self.address_line_1]
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        address_parts.extend([self.city, self.state, self.postal_code, self.country])
        return ', '.join(address_parts)


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile when a new User is created"""
    if created:
        UserProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the UserProfile when the User is saved"""
    if hasattr(instance, 'userprofile'):
        instance.userprofile.save()
